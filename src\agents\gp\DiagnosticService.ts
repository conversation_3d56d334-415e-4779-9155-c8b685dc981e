/**
 * GP DIAGNOSTIC SERVICE
 * 
 * Handles diagnostic assessment, SOAP framework implementation,
 * and clinical decision support for the General Practitioner Agent.
 */

import type {
  AgentRequest,
  SOAPAssessment,
  DiagnosticContext,
  EmergencyFlag,
  ClinicalDecisionSupport,
  SpecialistReferralCriteria
} from '../../types/agents';

export class GPDiagnosticService {
  
  /**
   * Initialize or update SOAP assessment for structured consultation
   */
  initializeOrUpdateSOAPAssessment(request: AgentRequest): SOAPAssessment {
    const sessionId = request.sessionId;
    const userMessage = request.userMessage.toLowerCase();
    
    // Create new SOAP assessment or update existing one
    const soapAssessment: SOAPAssessment = {
      sessionId,
      subjective: {
        chiefComplaint: this.extractChiefComplaint(userMessage),
        historyOfPresentIllness: this.extractHistoryOfPresentIllness(userMessage),
        reviewOfSystems: this.extractReviewOfSystems(userMessage),
        pastMedicalHistory: this.extractPastMedicalHistory(request),
        medications: this.extractCurrentMedications(request),
        allergies: this.extractAllergies(request),
        socialHistory: this.extractSocialHistory(request),
        familyHistory: this.extractFamilyHistory(request)
      },
      objective: {
        vitalSigns: this.extractVitalSigns(userMessage),
        physicalExam: this.extractPhysicalExamFindings(userMessage),
        diagnosticTests: this.extractDiagnosticTests(userMessage),
        observations: this.extractClinicalObservations(userMessage)
      },
      assessment: {
        primaryDiagnosis: this.generatePrimaryDiagnosis(userMessage),
        differentialDiagnoses: this.generateDifferentialDiagnoses(userMessage),
        clinicalImpression: this.generateClinicalImpression(userMessage),
        riskFactors: this.identifyRiskFactors(request),
        prognosis: this.assessPrognosis(userMessage)
      },
      plan: {
        treatments: this.recommendTreatments(userMessage),
        medications: this.recommendMedications(userMessage),
        diagnosticOrders: this.recommendDiagnosticTests(userMessage),
        referrals: this.recommendReferrals(userMessage),
        followUp: this.planFollowUp(userMessage),
        patientEducation: this.planPatientEducation(userMessage),
        lifestyle: this.recommendLifestyleChanges(userMessage)
      },
      timestamp: new Date(),
      confidence: this.calculateSOAPConfidence(userMessage),
      completeness: this.calculateSOAPCompleteness(userMessage)
    };

    return soapAssessment;
  }

  /**
   * Detect medical emergencies in patient messages
   */
  detectEmergencies(userMessage: string): EmergencyFlag[] {
    const emergencyFlags: EmergencyFlag[] = [];
    const message = userMessage.toLowerCase();

    // Critical emergency indicators
    const criticalIndicators = [
      'chest pain', 'difficulty breathing', 'severe bleeding', 'unconscious',
      'stroke symptoms', 'heart attack', 'severe allergic reaction', 'poisoning',
      'severe head injury', 'suicidal thoughts', 'can\'t breathe'
    ];

    // High priority emergency indicators
    const highPriorityIndicators = [
      'severe pain', 'high fever', 'vomiting blood', 'severe headache',
      'confusion', 'seizure', 'severe abdominal pain', 'broken bone'
    ];

    // Medium priority indicators
    const mediumPriorityIndicators = [
      'persistent fever', 'severe nausea', 'persistent vomiting',
      'severe diarrhea', 'difficulty swallowing', 'severe cough'
    ];

    // Check for critical emergencies
    for (const indicator of criticalIndicators) {
      if (message.includes(indicator)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'critical',
          description: `Critical emergency indicator detected: ${indicator}`,
          recommendedAction: 'Call emergency services immediately (911/999/112)',
          timeToResponse: 300 // 5 minutes maximum
        });
      }
    }

    // Check for high priority emergencies
    for (const indicator of highPriorityIndicators) {
      if (message.includes(indicator)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'high',
          description: `High priority medical concern: ${indicator}`,
          recommendedAction: 'Seek immediate medical attention at emergency department',
          timeToResponse: 900 // 15 minutes
        });
      }
    }

    // Check for medium priority indicators
    for (const indicator of mediumPriorityIndicators) {
      if (message.includes(indicator)) {
        emergencyFlags.push({
          type: 'medical_emergency',
          severity: 'medium',
          description: `Medical concern requiring prompt attention: ${indicator}`,
          recommendedAction: 'Contact healthcare provider within 24 hours',
          timeToResponse: 1800 // 30 minutes
        });
      }
    }

    return emergencyFlags;
  }

  /**
   * Analyze request for specialist referral needs
   */
  analyzeForSpecialistReferral(request: AgentRequest): SpecialistReferralCriteria[] {
    const referrals: SpecialistReferralCriteria[] = [];
    const message = request.userMessage.toLowerCase();

    // Cardiology referral indicators
    if (this.indicatesCardiologyReferral(message)) {
      referrals.push({
        specialty: 'cardiology',
        urgency: this.determineCardiologyUrgency(message),
        reason: 'Cardiovascular symptoms requiring specialist evaluation',
        expectedBenefit: 'Specialized cardiac assessment and treatment planning',
        patientPreparation: ['Bring list of current medications', 'Prepare symptom diary'],
        informationToProvide: ['ECG results', 'Blood pressure readings', 'Family cardiac history'],
        timeframe: this.determineReferralTimeframe('cardiology', message)
      });
    }

    // Dermatology referral indicators
    if (this.indicatesDermatologyReferral(message)) {
      referrals.push({
        specialty: 'dermatology',
        urgency: this.determineDermatologyUrgency(message),
        reason: 'Skin condition requiring specialist evaluation',
        expectedBenefit: 'Specialized dermatological diagnosis and treatment',
        patientPreparation: ['Take photos of affected areas', 'List previous treatments tried'],
        informationToProvide: ['Photos of skin condition', 'Previous biopsy results if any'],
        timeframe: this.determineReferralTimeframe('dermatology', message)
      });
    }

    // Mental health referral indicators
    if (this.indicatesMentalHealthReferral(message)) {
      referrals.push({
        specialty: 'mental_health',
        urgency: this.determineMentalHealthUrgency(message),
        reason: 'Mental health concerns requiring specialized support',
        expectedBenefit: 'Professional mental health assessment and treatment',
        patientPreparation: ['Prepare to discuss symptoms and triggers', 'List current stressors'],
        informationToProvide: ['Mental health history', 'Current medications', 'Support system'],
        timeframe: this.determineReferralTimeframe('mental_health', message)
      });
    }

    return referrals;
  }

  /**
   * Calculate confidence score for diagnostic assessment
   */
  calculateConfidence(request: AgentRequest): number {
    let confidence = 0.7; // Base confidence for GP consultations

    const message = request.userMessage.toLowerCase();
    
    // Increase confidence for clear, specific symptoms
    if (this.hasSpecificSymptoms(message)) {
      confidence += 0.1;
    }

    // Increase confidence if patient context is available
    if (request.patientContext) {
      confidence += 0.1;
    }

    // Decrease confidence for complex or specialized conditions
    if (this.isComplexCondition(message)) {
      confidence -= 0.2;
    }

    // Decrease confidence for emergency situations
    const emergencyFlags = this.detectEmergencies(message);
    if (emergencyFlags.some(flag => flag.severity === 'critical')) {
      confidence -= 0.3;
    }

    return Math.max(0.1, Math.min(1.0, confidence));
  }

  // Private helper methods for SOAP assessment
  private extractChiefComplaint(message: string): string {
    // Extract the main complaint from the message
    const complaintPatterns = [
      /i have (.*?)(?:\.|$)/i,
      /experiencing (.*?)(?:\.|$)/i,
      /suffering from (.*?)(?:\.|$)/i,
      /problem with (.*?)(?:\.|$)/i
    ];

    for (const pattern of complaintPatterns) {
      const match = message.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    // Fallback: return first sentence
    const sentences = message.split('.').filter(s => s.trim().length > 0);
    return sentences[0]?.trim() || 'Patient seeking medical consultation';
  }

  private extractHistoryOfPresentIllness(message: string): string {
    // Extract duration, onset, and progression information
    const durationPatterns = [
      /for (\d+) (days?|weeks?|months?)/i,
      /since (yesterday|last week|last month)/i,
      /started (\d+) (days?|weeks?|months?) ago/i
    ];

    let history = '';
    for (const pattern of durationPatterns) {
      const match = message.match(pattern);
      if (match) {
        history += `Duration: ${match[0]}. `;
        break;
      }
    }

    return history || 'Patient reports current symptoms';
  }

  private extractReviewOfSystems(message: string): string[] {
    const systems: string[] = [];
    const systemKeywords = {
      'constitutional': ['fever', 'chills', 'fatigue', 'weight loss', 'weight gain'],
      'cardiovascular': ['chest pain', 'palpitations', 'shortness of breath'],
      'respiratory': ['cough', 'wheezing', 'difficulty breathing'],
      'gastrointestinal': ['nausea', 'vomiting', 'diarrhea', 'constipation'],
      'neurological': ['headache', 'dizziness', 'weakness', 'numbness'],
      'musculoskeletal': ['joint pain', 'muscle pain', 'stiffness']
    };

    for (const [system, keywords] of Object.entries(systemKeywords)) {
      if (keywords.some(keyword => message.toLowerCase().includes(keyword))) {
        systems.push(system);
      }
    }

    return systems;
  }

  private extractPastMedicalHistory(request: AgentRequest): string[] {
    if (request.patientContext?.medicalHistory) {
      return request.patientContext.medicalHistory;
    }
    return [];
  }

  private extractCurrentMedications(request: AgentRequest): string[] {
    if (request.patientContext?.currentMedications) {
      return request.patientContext.currentMedications;
    }
    return [];
  }

  private extractAllergies(request: AgentRequest): string[] {
    if (request.patientContext?.allergies) {
      return request.patientContext.allergies;
    }
    return [];
  }

  private extractSocialHistory(request: AgentRequest): string {
    // Extract social history from patient context or message
    return request.patientContext?.socialHistory || '';
  }

  private extractFamilyHistory(request: AgentRequest): string[] {
    if (request.patientContext?.familyHistory) {
      return request.patientContext.familyHistory;
    }
    return [];
  }

  private extractVitalSigns(message: string): any {
    const vitalSigns: any = {};
    
    // Blood pressure pattern
    const bpPattern = /(\d{2,3})\/(\d{2,3})/;
    const bpMatch = message.match(bpPattern);
    if (bpMatch) {
      vitalSigns.bloodPressure = `${bpMatch[1]}/${bpMatch[2]}`;
    }

    // Temperature pattern
    const tempPattern = /(\d{2,3}(?:\.\d)?)\s*(?:degrees?|°)\s*(?:celsius|fahrenheit|c|f)?/i;
    const tempMatch = message.match(tempPattern);
    if (tempMatch) {
      vitalSigns.temperature = parseFloat(tempMatch[1]);
    }

    // Heart rate pattern
    const hrPattern = /heart rate\s*:?\s*(\d{2,3})/i;
    const hrMatch = message.match(hrPattern);
    if (hrMatch) {
      vitalSigns.heartRate = parseInt(hrMatch[1]);
    }

    return vitalSigns;
  }

  private extractPhysicalExamFindings(message: string): string[] {
    // Extract physical examination findings mentioned in the message
    const findings: string[] = [];
    const examKeywords = [
      'swelling', 'redness', 'tenderness', 'rash', 'bruising',
      'enlarged', 'normal', 'abnormal', 'palpable'
    ];

    for (const keyword of examKeywords) {
      if (message.toLowerCase().includes(keyword)) {
        findings.push(`Patient reports ${keyword}`);
      }
    }

    return findings;
  }

  private extractDiagnosticTests(message: string): string[] {
    // Extract any diagnostic tests mentioned
    const tests: string[] = [];
    const testKeywords = [
      'blood test', 'x-ray', 'mri', 'ct scan', 'ultrasound',
      'ecg', 'ekg', 'blood work', 'lab results'
    ];

    for (const test of testKeywords) {
      if (message.toLowerCase().includes(test)) {
        tests.push(test);
      }
    }

    return tests;
  }

  private extractClinicalObservations(message: string): string[] {
    // Extract clinical observations from the message
    return [`Patient communication: ${message.substring(0, 100)}...`];
  }

  private generatePrimaryDiagnosis(message: string): string {
    // Simple pattern matching for common conditions
    const conditions = {
      'cold': ['runny nose', 'sneezing', 'congestion'],
      'flu': ['fever', 'body aches', 'fatigue'],
      'headache': ['headache', 'head pain'],
      'back pain': ['back pain', 'lower back'],
      'anxiety': ['anxious', 'worried', 'stress']
    };

    for (const [condition, symptoms] of Object.entries(conditions)) {
      if (symptoms.some(symptom => message.toLowerCase().includes(symptom))) {
        return `Possible ${condition}`;
      }
    }

    return 'Requires further evaluation';
  }

  private generateDifferentialDiagnoses(message: string): string[] {
    // Generate differential diagnoses based on symptoms
    return ['Alternative diagnosis 1', 'Alternative diagnosis 2'];
  }

  private generateClinicalImpression(message: string): string {
    return 'Patient presents with symptoms requiring clinical evaluation and appropriate management.';
  }

  private identifyRiskFactors(request: AgentRequest): string[] {
    const riskFactors: string[] = [];
    
    if (request.patientContext?.age && request.patientContext.age > 65) {
      riskFactors.push('Advanced age');
    }

    return riskFactors;
  }

  private assessPrognosis(message: string): string {
    return 'Good with appropriate treatment and follow-up';
  }

  private recommendTreatments(message: string): string[] {
    return ['Symptomatic treatment', 'Rest and hydration'];
  }

  private recommendMedications(message: string): string[] {
    return ['Over-the-counter pain relief as needed'];
  }

  private recommendDiagnosticTests(message: string): string[] {
    return ['Consider basic laboratory studies if symptoms persist'];
  }

  private recommendReferrals(message: string): string[] {
    return [];
  }

  private planFollowUp(message: string): string[] {
    return ['Follow up in 1-2 weeks if symptoms persist or worsen'];
  }

  private planPatientEducation(message: string): string[] {
    return ['Discuss symptom management', 'Provide condition-specific education'];
  }

  private recommendLifestyleChanges(message: string): string[] {
    return ['Maintain healthy lifestyle', 'Adequate rest and nutrition'];
  }

  private calculateSOAPConfidence(message: string): number {
    // Calculate confidence based on information completeness
    let confidence = 0.5;
    
    if (message.length > 50) confidence += 0.2;
    if (this.hasSpecificSymptoms(message)) confidence += 0.2;
    
    return Math.min(1.0, confidence);
  }

  private calculateSOAPCompleteness(message: string): number {
    // Calculate completeness based on SOAP sections covered
    let completeness = 0.3; // Base completeness
    
    if (this.hasSubjectiveInfo(message)) completeness += 0.3;
    if (this.hasObjectiveInfo(message)) completeness += 0.2;
    if (this.hasAssessmentInfo(message)) completeness += 0.2;
    
    return Math.min(1.0, completeness);
  }

  // Helper methods for referral analysis
  private indicatesCardiologyReferral(message: string): boolean {
    const cardiacKeywords = ['chest pain', 'heart', 'palpitations', 'shortness of breath'];
    return cardiacKeywords.some(keyword => message.includes(keyword));
  }

  private indicatesDermatologyReferral(message: string): boolean {
    const skinKeywords = ['rash', 'skin', 'mole', 'acne', 'eczema'];
    return skinKeywords.some(keyword => message.includes(keyword));
  }

  private indicatesMentalHealthReferral(message: string): boolean {
    const mentalHealthKeywords = ['depression', 'anxiety', 'stress', 'mental health'];
    return mentalHealthKeywords.some(keyword => message.includes(keyword));
  }

  private determineCardiologyUrgency(message: string): 'routine' | 'urgent' | 'emergent' {
    if (message.includes('chest pain') || message.includes('heart attack')) {
      return 'emergent';
    }
    return 'routine';
  }

  private determineDermatologyUrgency(message: string): 'routine' | 'urgent' | 'emergent' {
    return 'routine';
  }

  private determineMentalHealthUrgency(message: string): 'routine' | 'urgent' | 'emergent' {
    if (message.includes('suicidal') || message.includes('self-harm')) {
      return 'emergent';
    }
    return 'routine';
  }

  private determineReferralTimeframe(specialty: string, message: string): string {
    switch (specialty) {
      case 'cardiology':
        return message.includes('chest pain') ? 'Within 24 hours' : 'Within 2 weeks';
      case 'mental_health':
        return message.includes('suicidal') ? 'Immediately' : 'Within 1 week';
      default:
        return 'Within 4 weeks';
    }
  }

  private hasSpecificSymptoms(message: string): boolean {
    const specificSymptoms = ['pain', 'fever', 'headache', 'nausea', 'cough'];
    return specificSymptoms.some(symptom => message.includes(symptom));
  }

  private isComplexCondition(message: string): boolean {
    const complexKeywords = ['cancer', 'surgery', 'chronic', 'rare'];
    return complexKeywords.some(keyword => message.includes(keyword));
  }

  private hasSubjectiveInfo(message: string): boolean {
    return message.length > 20;
  }

  private hasObjectiveInfo(message: string): boolean {
    return /\d/.test(message); // Contains numbers (vital signs, measurements)
  }

  private hasAssessmentInfo(message: string): boolean {
    const assessmentKeywords = ['diagnosis', 'condition', 'problem'];
    return assessmentKeywords.some(keyword => message.includes(keyword));
  }
}

export const gpDiagnosticService = new GPDiagnosticService();
